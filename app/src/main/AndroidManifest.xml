<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="com.pax.permission.UPDATE_APP" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:extractNativeLibs="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_tts_hindi"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_tts_hindi_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.IN_TTS_Hindi_Pack_Injector"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.IN_TTS_Hindi_Pack_Injector">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".DebugActivity"
            android:exported="true"
            android:label="TTS Debug"
            android:theme="@style/Theme.IN_TTS_Hindi_Pack_Injector">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>

</manifest>