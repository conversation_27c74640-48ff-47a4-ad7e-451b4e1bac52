package com.example.in_tts_hindi_pack_injector

import android.app.Application
import android.content.Context
import android.util.Log
import com.pax.dal.IDAL
import com.pax.neptunelite.api.NeptuneLiteUser

class App : Application() {
    var dal: IDAL? = null
        private set

    companion object {
        private lateinit var instance: App
        private const val TAG = "App"
        fun get(): App {
            return instance
        }
    }

    override fun onCreate() {
        super.onCreate()
        instance = this
        initNeptuneDal(applicationContext)
    }

    /**
     * 初始化 Neptune DAL。
     */
    private fun initNeptuneDal(context: Context) {
        try {
            dal = NeptuneLiteUser.getInstance().getDal(context)
            if (dal != null) {
                Log.d(TAG, "Neptune DAL initialized successfully.")
            } else {
                Log.e(TAG, "Neptune DAL initialization returned null.")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Neptune DAL", e)
            dal = null
        }
    }
}