package com.example.in_tts_hindi_pack_injector

import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.in_tts_hindi_pack_injector.ui.theme.IN_TTS_Hindi_Pack_InjectorTheme
import java.io.File

class DebugActivity : ComponentActivity() {
    
    companion object {
        private const val TAG = "DebugActivity"
    }
    
    private var debugInfo by mutableStateOf("Initializing debug info...")
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            IN_TTS_Hindi_Pack_InjectorTheme {
                DebugScreen(
                    debugInfo = debugInfo,
                    onRefresh = { collectDebugInfo() },
                    onTestTTS = { testTTSInstallation() }
                )
            }
        }
        
        collectDebugInfo()
    }
    
    private fun collectDebugInfo() {
        val info = StringBuilder()
        
        try {
            // App info
            info.append("=== APP DEBUG INFO ===\n")
            info.append("Package: ${packageName}\n")
            info.append("Version: ${packageManager.getPackageInfo(packageName, 0).versionName}\n\n")
            
            // DAL info
            info.append("=== DAL INFO ===\n")
            val app = App.get()
            info.append("App instance: ${if (app != null) "OK" else "NULL"}\n")
            info.append("DAL instance: ${if (app.dal != null) "OK" else "NULL"}\n")
            
            if (app.dal != null) {
                try {
                    val sys = app.dal?.getSys()
                    info.append("ISys instance: ${if (sys != null) "OK" else "NULL"}\n")
                } catch (e: Exception) {
                    info.append("ISys error: ${e.message}\n")
                }
            }
            info.append("\n")
            
            // Storage info
            info.append("=== STORAGE INFO ===\n")
            info.append("External storage state: ${Environment.getExternalStorageState()}\n")
            info.append("External storage dir: ${Environment.getExternalStorageDirectory()}\n")
            
            val externalDir = Environment.getExternalStorageDirectory()
            info.append("External dir exists: ${externalDir.exists()}\n")
            info.append("External dir readable: ${externalDir.canRead()}\n")
            info.append("External dir writable: ${externalDir.canWrite()}\n\n")
            
            // Assets info
            info.append("=== ASSETS INFO ===\n")
            try {
                val assetFiles = assets.list("")
                info.append("Assets found: ${assetFiles?.size ?: 0}\n")
                assetFiles?.forEach { file ->
                    if (file.contains("hi-in")) {
                        info.append("- $file\n")
                        try {
                            val inputStream = assets.open(file)
                            val size = inputStream.available()
                            inputStream.close()
                            info.append("  Size: $size bytes\n")
                        } catch (e: Exception) {
                            info.append("  Error reading: ${e.message}\n")
                        }
                    }
                }
            } catch (e: Exception) {
                info.append("Error listing assets: ${e.message}\n")
            }
            info.append("\n")
            
            // File system check
            info.append("=== FILE SYSTEM CHECK ===\n")
            val testFiles = listOf("hi-in_sign.zip", "hi-in_voice_sign.zip")
            testFiles.forEach { fileName ->
                val file = File(externalDir, fileName)
                info.append("$fileName:\n")
                info.append("  Exists: ${file.exists()}\n")
                if (file.exists()) {
                    info.append("  Size: ${file.length()} bytes\n")
                    info.append("  Readable: ${file.canRead()}\n")
                }
            }
            
        } catch (e: Exception) {
            info.append("Error collecting debug info: ${e.message}\n")
            Log.e(TAG, "Error collecting debug info", e)
        }
        
        debugInfo = info.toString()
        Log.i(TAG, "Debug info collected:\n$debugInfo")
    }
    
    private fun testTTSInstallation() {
        try {
            Log.i(TAG, "Testing TTS installation...")
            Toast.makeText(this, "Testing TTS installation...", Toast.LENGTH_SHORT).show()
            
            // This would normally be done by TTSInstallWorker
            val app = App.get()
            val dal = app.dal
            val sys = dal?.getSys()
            
            if (sys == null) {
                Toast.makeText(this, "Error: ISys is null", Toast.LENGTH_LONG).show()
                return
            }
            
            val assetFileName = "hi-in_sign.zip"
            val externalDir = Environment.getExternalStorageDirectory()
            val destFile = File(externalDir, assetFileName)
            
            if (!destFile.exists()) {
                // Try to copy from assets
                try {
                    assets.open(assetFileName).use { input ->
                        destFile.outputStream().use { output ->
                            input.copyTo(output)
                        }
                    }
                    Log.i(TAG, "File copied to: ${destFile.absolutePath}")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to copy file", e)
                    Toast.makeText(this, "Failed to copy file: ${e.message}", Toast.LENGTH_LONG).show()
                    return
                }
            }
            
            // Test the actual TTS update
            val result = sys.updateTTSVoices(destFile.absolutePath)
            val message = "TTS update result: $result"
            Log.i(TAG, message)
            Toast.makeText(this, message, Toast.LENGTH_LONG).show()
            
        } catch (e: Exception) {
            val message = "Test failed: ${e.message}"
            Log.e(TAG, message, e)
            Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        }
    }
}

@Composable
fun DebugScreen(
    debugInfo: String,
    onRefresh: () -> Unit,
    onTestTTS: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                text = "TTS Debug Information",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onRefresh,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Refresh Info")
                }
                
                Button(
                    onClick = onTestTTS,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Test TTS")
                }
            }
            
            Card(
                modifier = Modifier.fillMaxSize()
            ) {
                Text(
                    text = debugInfo,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                        .verticalScroll(rememberScrollState()),
                    fontFamily = FontFamily.Monospace,
                    fontSize = 12.sp
                )
            }
        }
    }
}
