package com.example.in_tts_hindi_pack_injector

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.work.*
import com.example.in_tts_hindi_pack_injector.ui.theme.IN_TTS_Hindi_Pack_InjectorTheme
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    private var installationStatus by mutableStateOf("Preparing installation...")
    private var isInstalling by mutableStateOf(true)
    private var installationResult by mutableStateOf<Boolean?>(null)

    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            startInstallation()
        } else {
            installationStatus = "Permission denied, cannot install voice pack"
            isInstalling = false
            installationResult = false
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            IN_TTS_Hindi_Pack_InjectorTheme {
                InstallationScreen(
                    status = installationStatus,
                    isInstalling = isInstalling,
                    result = installationResult,
                    onExit = { uninstallSelf() }
                )
            }
        }

        // 检查权限并开始安装
        checkPermissionsAndStart()
    }

    private fun checkPermissionsAndStart() {
        // Android 13+ 不再需要 READ_EXTERNAL_STORAGE 权限来访问应用自己的外部存储
        // 但我们仍然需要检查存储是否可用
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 直接开始安装
            Log.i(TAG, "Android 13+, no storage permissions needed")
            startInstallation()
            return
        }

        val permissions = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )

        val allPermissionsGranted = permissions.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }

        if (allPermissionsGranted) {
            Log.i(TAG, "All storage permissions granted")
            startInstallation()
        } else {
            Log.i(TAG, "Requesting storage permissions")
            requestPermissionLauncher.launch(permissions)
        }
    }

    private fun startInstallation() {
        Log.i(TAG, "Starting TTS installation process")
        installationStatus = "Installing Hindi voice pack..."

        try {
            // 创建工作请求
            val workRequest = OneTimeWorkRequestBuilder<TTSInstallWorker>()
                .build()

            // 启动工作
            val workManager = WorkManager.getInstance(this)
            workManager.enqueue(workRequest)
            Log.i(TAG, "Work request enqueued with ID: ${workRequest.id}")

            // 观察工作状态
            lifecycleScope.launch {
                workManager.getWorkInfoByIdLiveData(workRequest.id).observe(this@MainActivity) { workInfo ->
                    Log.d(TAG, "Work state changed: ${workInfo?.state}")
                    when (workInfo?.state) {
                        WorkInfo.State.RUNNING -> {
                            installationStatus = "Installing Hindi voice pack..."
                            isInstalling = true
                            Log.i(TAG, "TTS installation is running")
                        }
                        WorkInfo.State.SUCCEEDED -> {
                            isInstalling = false
                            installationResult = true
                            installationStatus = "Hindi voice pack installed successfully!"
                            Log.i(TAG, "TTS installation succeeded")
                            try {
                                Toast.makeText(
                                    this@MainActivity,
                                    installationStatus,
                                    Toast.LENGTH_LONG
                                ).show()
                            } catch (e: Exception) {
                                Log.e(TAG, "Failed to show success toast", e)
                            }
                        }
                        WorkInfo.State.FAILED -> {
                            isInstalling = false
                            installationResult = false
                            installationStatus = "Hindi voice pack installation failed, please check logs"
                            Log.e(TAG, "TTS installation failed")
                            try {
                                Toast.makeText(
                                    this@MainActivity,
                                    installationStatus,
                                    Toast.LENGTH_LONG
                                ).show()
                            } catch (e: Exception) {
                                Log.e(TAG, "Failed to show failure toast", e)
                            }
                        }
                        else -> {
                            // 其他状态（ENQUEUED, BLOCKED, CANCELLED）
                            Log.d(TAG, "Work state: ${workInfo?.state}")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start installation work", e)
            isInstalling = false
            installationResult = false
            installationStatus = "Failed to start installation: ${e.message}"
        }
    }

    private fun uninstallSelf() {
        val iSys = App.get().dal?.getSys()
        if (iSys == null) {
            Log.e(TAG, "ISys is null, cannot uninstall app")
            Toast.makeText(this, "Failed to uninstall: System interface not available", Toast.LENGTH_LONG).show()
            finishAndRemoveTask()
            return
        }

        try {
            val packageName = packageName
            Log.i(TAG, "Attempting to uninstall app: $packageName")

            val result = iSys.uninstallApp(packageName)

            when (result) {
                0 -> {
                    Log.i(TAG, "App uninstall initiated successfully")
                    // 成功时不显示Toast，因为应用即将被卸载
                }
                -101 -> {
                    Log.e(TAG, "Uninstall failed: UPDATE_UNKNOWN_ERR")
                    Toast.makeText(this, "Uninstall failed: Unknown error", Toast.LENGTH_LONG).show()
                }
                -99 -> {
                    Log.e(TAG, "Uninstall failed: UPDATE_PERMISSION_ERROR")
                    Toast.makeText(this, "Uninstall failed: Permission error", Toast.LENGTH_LONG).show()
                }
                -50 -> {
                    Log.e(TAG, "Uninstall failed: PKG_OR_CLASS_NAME_ERROR")
                    Toast.makeText(this, "Uninstall failed: Package name error", Toast.LENGTH_LONG).show()
                }
                1 -> {
                    Log.e(TAG, "Uninstall failed: SERVICE_NOT_AVAILABLE")
                    Toast.makeText(this, "Uninstall failed: Service not available", Toast.LENGTH_LONG).show()
                }
                2 -> {
                    Log.e(TAG, "Uninstall failed: INSTALL_FAIL")
                    Toast.makeText(this, "Uninstall failed: Operation failed", Toast.LENGTH_LONG).show()
                }
                3 -> {
                    Log.e(TAG, "Uninstall failed: TIMEOUT_ERR")
                    Toast.makeText(this, "Uninstall failed: Timeout error", Toast.LENGTH_LONG).show()
                }
                else -> {
                    Log.e(TAG, "Uninstall failed with error code: $result")
                    Toast.makeText(this, "Uninstall failed: Error code $result", Toast.LENGTH_LONG).show()
                }
            }

            // If uninstall fails, fall back to normal app exit
            if (result != 0) {
                finishAndRemoveTask()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Exception during app uninstall", e)
            Toast.makeText(this, "Uninstall failed: ${e.message}", Toast.LENGTH_LONG).show()
            finishAndRemoveTask()
        }
    }
}

@Composable
fun InstallationScreen(
    status: String,
    isInstalling: Boolean,
    result: Boolean?,
    onExit: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // 标题
            Text(
                text = "Hindi TTS Voice Pack Installer",
                style = MaterialTheme.typography.headlineMedium,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // 状态图标
            when {
                isInstalling -> {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .size(64.dp)
                            .padding(bottom = 24.dp),
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                result == true -> {
                    Text(
                        text = "✅",
                        style = MaterialTheme.typography.displayLarge,
                        modifier = Modifier.padding(bottom = 24.dp)
                    )
                }
                result == false -> {
                    Text(
                        text = "❌",
                        style = MaterialTheme.typography.displayLarge,
                        modifier = Modifier.padding(bottom = 24.dp)
                    )
                }
            }

            // 状态文本
            Text(
                text = status,
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            // 卸载按钮（仅在安装完成后显示）
            if (!isInstalling) {
                Button(
                    onClick = onExit,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp)
                ) {
                    Text("Uninstall App")
                }
            }
        }
    }
}