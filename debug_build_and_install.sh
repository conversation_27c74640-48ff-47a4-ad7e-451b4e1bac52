#!/bin/bash

# Debug script for TTS Hindi Pack Injector
# This script helps build and install the app with proper debugging

echo "=== TTS Hindi Pack Injector Debug Build Script ==="

# Check if we're in the right directory
if [ ! -f "app/build.gradle.kts" ]; then
    echo "Error: Not in the project root directory"
    echo "Please run this script from the project root"
    exit 1
fi

# Clean previous builds
echo "Cleaning previous builds..."
./gradlew clean

# Build debug APK
echo "Building debug APK..."
./gradlew assembleDebug

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "Build failed! Please check the error messages above."
    exit 1
fi

# Find the APK file
APK_PATH=$(find app/build/outputs/apk/debug -name "*.apk" | head -1)

if [ -z "$APK_PATH" ]; then
    echo "Error: Could not find the built APK file"
    exit 1
fi

echo "APK built successfully: $APK_PATH"

# Check if device is connected
adb devices | grep -q "device$"
if [ $? -ne 0 ]; then
    echo "Error: No Android device connected or device not authorized"
    echo "Please connect your device and enable USB debugging"
    exit 1
fi

# Uninstall previous version if exists
echo "Uninstalling previous version..."
adb uninstall com.example.in_tts_hindi_pack_injector 2>/dev/null

# Install new APK
echo "Installing new APK..."
adb install "$APK_PATH"

if [ $? -ne 0 ]; then
    echo "Installation failed!"
    exit 1
fi

echo "Installation successful!"

# Start logcat to monitor the app
echo "Starting logcat to monitor app logs..."
echo "Press Ctrl+C to stop monitoring"
echo "=== App Logs ==="

# Clear logcat buffer first
adb logcat -c

# Start the app
adb shell am start -n com.example.in_tts_hindi_pack_injector/.MainActivity

# Monitor logs with filters for our app
adb logcat | grep -E "(TTSInstallWorker|MainActivity|App|in_tts_hindi_pack_injector)"
